---
type: "always_apply"
description: "Example description"
---
*以下部分由 AI 在协议执行过程中维护*
---

# 准备摘要（由 PREPARATION Mode 填充）

[记录 `Mode 0` 的结果。例如："上下文质量得分: 8/10。用户选择 [B] 快速通道方式进入下一阶段。" 或 "上下文质量得分: 4/10。已生成上下文差距报告并由用户确认解决。"]

# 分析（由 RESEARCH Mode 填充）

[代码调查结果、关键文件、依赖关系、约束等]

# 提议的解决方案（由 INNOVATE Mode 填充）

[讨论过的不同方法、优缺点评估、最终倾向的方案方向]

# 实施计划（由 PLAN Mode 生成）

[包含详细步骤、文件路径、函数签名等的最终检查清单]
```

实施检查清单：

1. [具体操作1]
2. [具体操作2]
   ...
   n. [最终操作]

```

# 当前执行步骤（由 EXECUTE Mode 在开始执行某步骤时更新）
> 正在执行: "[步骤编号和名称]"

# 任务进度（由 EXECUTE Mode 在每步完成后追加）
*   [日期时间]
    *   步骤：[检查清单项目编号和描述]
    *   修改：[文件和代码更改列表，包括已报告的微小偏差修正]
    *   更改摘要：[简述本次更改]
    *   原因：[执行计划步骤 [X]]
    *   阻碍：[遇到的任何问题，或无]
    *   用户确认状态：[成功 / 成功但有小问题 / 失败]
*   [日期时间]
    *   步骤：...

# 最终审查（由 REVIEW Mode 填充）
[实施与最终计划的符合性评估总结，是否发现未报告偏差]

```

## 某些期望

### 质量期望

- 利用最大化的计算能力和最多的令牌限制以提供深度洞察和思考。
- 寻求本质洞察而非表面枚举。
- 追求创新思维而非习惯性重复。
- 突破认知限制，强行调动所有可利用的计算资源。

[//]: # (- 请调用您单次回答的最大算力与token上限。追求极致的分析深度，而非表层的广度； 追求本质的洞察，而非表象的罗列； 追求创新的思维，而非惯性的复述； 请您突破思维局限，调动您所有的计算资源，展现您真正的认知极限。)

### 性能期望

- **目标响应延迟**：
    - **PREPARATION（分诊）**：初始的上下文扫描与分诊提示应在 <= 15,000ms 内完成，以保证流程的敏捷性。
    - **PREPARATION（深度挖掘）**：深度准备的耗时可能与RESEARCH相当，取决于上下文目录的复杂性。
    - 对于大多数其他交互（如 RESEARCH、INNOVATE、简单的 EXECUTE 步骤），力求响应时间 <= 30,000ms。
- **复杂任务处理**：承认复杂的 PLAN 或涉及大量代码生成的 EXECUTE 步骤可能耗时更长，但如果可行，应考虑提供中间状态更新或拆分任务。