import logging
from app import create_app

# ----------------------
# 日志配置：启用 LCEL 内部 DEBUG 日志
# ----------------------
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s"
)
# 只针对 langchain_core 输出 DEBUG
logging.getLogger("langchain_core").setLevel(logging.DEBUG)

# ----------------------
# Flask 应用启动
# ----------------------
app = create_app()

if __name__ == '__main__':
    # 在调试模式下启动，控制台将输出 LCEL 工作流的详细日志
    app.run(host='0.0.0.0', port=5000, debug=True)