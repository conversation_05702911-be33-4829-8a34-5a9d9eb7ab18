<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="36">
            <item index="0" class="java.lang.String" itemvalue="pre-commit" />
            <item index="1" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="2" class="java.lang.String" itemvalue="tiktoken" />
            <item index="3" class="java.lang.String" itemvalue="gradio" />
            <item index="4" class="java.lang.String" itemvalue="faiss-cpu" />
            <item index="5" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="6" class="java.lang.String" itemvalue="pytesseract" />
            <item index="7" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="8" class="java.lang.String" itemvalue="requests" />
            <item index="9" class="java.lang.String" itemvalue="pandas" />
            <item index="10" class="java.lang.String" itemvalue="tqdm" />
            <item index="11" class="java.lang.String" itemvalue="langchain-community" />
            <item index="12" class="java.lang.String" itemvalue="pdf2image" />
            <item index="13" class="java.lang.String" itemvalue="langchain" />
            <item index="14" class="java.lang.String" itemvalue="fake_useragent" />
            <item index="15" class="java.lang.String" itemvalue="numpy" />
            <item index="16" class="java.lang.String" itemvalue="pdfminer.six" />
            <item index="17" class="java.lang.String" itemvalue="pydantic" />
            <item index="18" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="19" class="java.lang.String" itemvalue="gradio_client" />
            <item index="20" class="java.lang.String" itemvalue="aiofiles" />
            <item index="21" class="java.lang.String" itemvalue="scrapy-redis" />
            <item index="22" class="java.lang.String" itemvalue="jieba" />
            <item index="23" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="24" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="25" class="java.lang.String" itemvalue="cryptography" />
            <item index="26" class="java.lang.String" itemvalue="scrapy" />
            <item index="27" class="java.lang.String" itemvalue="redis" />
            <item index="28" class="java.lang.String" itemvalue="sqlalchemy" />
            <item index="29" class="java.lang.String" itemvalue="pymysql" />
            <item index="30" class="java.lang.String" itemvalue="matplotlib" />
            <item index="31" class="java.lang.String" itemvalue="flask-cors" />
            <item index="32" class="java.lang.String" itemvalue="apscheduler" />
            <item index="33" class="java.lang.String" itemvalue="fake-useragent" />
            <item index="34" class="java.lang.String" itemvalue="lxml" />
            <item index="35" class="java.lang.String" itemvalue="flask" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="alibabacloud_bailian20231229.models.ApplyFileUploadLeaseResponseBodyData.*" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>