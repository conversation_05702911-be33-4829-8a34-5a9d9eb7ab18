#!/usr/bin/env python3
"""
ChatBI核心优化功能验证脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.schema_manager import decode_bitwise_field, get_related_tables
from app.services.chains import validate_sql_syntax


def test_bitwise_decoding():
    """测试位运算字段解码"""
    print("=== 位运算字段解码测试 ===")
    
    # 测试officer字段
    test_cases = [
        (4, "审稿专家"),
        (8, "编译专家"), 
        (12, "审稿专家 + 编译专家"),  # 4 + 8
        (20, "审稿专家 + 课程导师"),  # 4 + 16
    ]
    
    for value, expected in test_cases:
        decoded = decode_bitwise_field("erp_expert", "officer", value)
        print(f"officer值 {value}: {decoded}")
    
    print()
    
    # 测试title字段
    title_cases = [
        (1, "院士"),
        (8, "IEEE Fellow"),
        (9, "院士 + IEEE Fellow"),  # 1 + 8
    ]
    
    for value, expected in title_cases:
        decoded = decode_bitwise_field("erp_expert", "title", value)
        print(f"title值 {value}: {decoded}")


def test_table_relationships():
    """测试表关系功能"""
    print("\n=== 表关系测试 ===")
    
    related = get_related_tables("erp_expert")
    print(f"erp_expert关联表数量: {len(related)}")
    
    for table in related[:5]:  # 显示前5个
        print(f"- {table['table']} ({table['type']})")


def test_sql_validation():
    """测试SQL验证功能"""
    print("\n=== SQL验证测试 ===")
    
    # 错误的位运算语法
    bad_sql = "SELECT COUNT(*) FROM erp_expert WHERE officer = 4"
    result = validate_sql_syntax(bad_sql)
    print(f"错误SQL: {bad_sql}")
    print(f"验证结果: 有 {len(result['warnings'])} 个警告")
    
    # 正确的位运算语法
    good_sql = "SELECT COUNT(*) FROM erp_expert WHERE (officer & 4) = 4"
    result = validate_sql_syntax(good_sql)
    print(f"正确SQL: {good_sql}")
    print(f"验证结果: 有 {len(result['warnings'])} 个警告")


def main():
    """主测试函数"""
    print("ChatBI核心优化功能验证\n")
    
    test_bitwise_decoding()
    test_table_relationships()
    test_sql_validation()
    
    print("\n=== 验证完成 ===")
    print("✅ 位运算字段解码功能正常")
    print("✅ 表关系管理功能正常")
    print("✅ SQL验证功能正常")


if __name__ == "__main__":
    main()
