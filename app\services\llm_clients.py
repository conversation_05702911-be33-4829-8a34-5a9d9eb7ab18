import os
from dotenv import load_dotenv,find_dotenv
from langchain_openai import ChatOpenAI
import requests
# 加载 .env 文件中的环境变量
load_dotenv(find_dotenv(), override=True)

# 从环境变量中获取API配置
api_base = os.getenv("OPENAI_API_BASE")
api_key = os.getenv("OPENAI_API_KEY")
model_name = os.getenv("OPENAI_MODEL_NAME")

print(f"api_base: {api_base}, api_key: {api_key}, model_name: {model_name}")

if not all([api_base, api_key, model_name]):
    raise ValueError("一个或多个OpenAI环境变量未设置 (OPENAI_API_BASE, OPENAI_API_KEY, OPENAI_MODEL_NAME)。")

# 添加一个简单的函数来验证API密钥
def verify_api_key(base_url, api_key):
    """验证API密钥是否有效"""
    try:
        headers = {
            "Authorization": f"Bearer {api_key}"
        }
        # 尝试一个简单的API调用，如获取模型列表
        response = requests.get(f"{base_url}/models", headers=headers)
        if response.status_code == 200:
            print("API密钥验证成功!")
            return True
        else:
            print(f"API密钥验证失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"API密钥验证过程中发生错误: {e}")
        return False

# 在创建LLM客户端之前验证API密钥
if api_base and api_key:
    is_valid = verify_api_key(api_base, api_key)
    if not is_valid:
        print("警告：API密钥验证失败，LLM客户端可能无法正常工作")

# 用于SQL生成的LLM（代理）
# 用于SQL生成的LLM，温度设置为0以保证稳定和精确
sql_llm = ChatOpenAI(
    model=model_name,
    temperature=0,
    streaming=True,
    openai_api_base=api_base,
    openai_api_key=api_key
)

# 用于结果解读的LLM
# 用于结果解读的LLM，可以有略高的温度以产生更自然的语言
interpretation_llm = ChatOpenAI(
    model=model_name,
    temperature=0.7,
    streaming=True,
    openai_api_base=api_base,
    openai_api_key=api_key
)
