"""
数据库表关系管理模块。

由于数据库没有外键约束，此模块提供了表之间逻辑关系的明确定义。
"""

from typing import Dict, List, Tuple, Any

# 表关系定义
TABLE_RELATIONSHIPS = {
    "erp_expert": {
        "info": {
            "related_table": "erp_expert_info",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_one"  # erp_expert_info 的主键是 expertId
        },
        "applications": {
            "related_table": "erp_expert_apply",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_many"  # erp_expert_apply 中有 expertId 字段并创建了索引
        },
        "developments": {
            "related_table": "erp_expert_dev",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_many"  # erp_expert_dev 中有 expertId 字段并创建了索引
        },
        "favorites": {
            "related_table": "erp_expert_favorite",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_many"  # erp_expert_favorite 中有 expertId 字段并创建了索引
        },
        "logs": {
            "related_table": "erp_expert_log",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_many"  # erp_expert_log 中有 expertId 字段并创建了索引
        },
        "resumes": {
            "related_table": "erp_expert_resume",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_many"  # erp_expert_resume 中有 expertId 字段并创建了索引
        },
        "supports": {
            "related_table": "erp_expert_support",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_many"  # erp_expert_support 中有 expertId 字段并创建了索引 
        },
        "tags_relation": {
            "related_table": "erp_expert_tag",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_many"  # erp_expert_tag 中有 expertId 字段和唯一索引
        },
        "works": {
            "related_table": "erp_expert_work",
            "local_key": "id",
            "foreign_key": "expertId",
            "relation_type": "one_to_many"  # erp_expert_work 中有 expertId 字段并创建了索引
        }
    }
}

# 位运算字段定义
BITWISE_FIELDS = {
    "erp_expert": {
        "officer": {
            4: "审稿专家",
            8: "编译专家",
            16: "课程导师",
            32: "会议嘉宾",
            64: "头条创作者"
        },
        "title": {
            1: "院士",
            2: "国家级高层次人才",
            4: "国家级青年人才",
            8: "IEEE Fellow",
            16: "ACM Fellow"
        }
    }
}

def get_related_tables(table_name: str) -> List[Dict[str, Any]]:
    """获取指定表的所有关联表信息"""
    if table_name not in TABLE_RELATIONSHIPS:
        return []
    
    return [
        {
            "table": rel_info["related_table"],
            "local_key": rel_info["local_key"],
            "foreign_key": rel_info["foreign_key"],
            "type": rel_info["relation_type"]
        }
        for field, rel_info in TABLE_RELATIONSHIPS[table_name].items()
    ]

def get_join_condition(main_table: str, related_table: str) -> Tuple[str, str]:
    """获取两个表之间的JOIN条件"""
    if main_table not in TABLE_RELATIONSHIPS:
        return None, None
    
    for field, rel_info in TABLE_RELATIONSHIPS[main_table].items():
        if rel_info["related_table"] == related_table:
            return field, rel_info["foreign_key"]
    
    return None, None

def decode_bitwise_field(table: str, field: str, value: int) -> List[str]:
    """解码位运算字段的值"""
    if (table not in BITWISE_FIELDS or 
        field not in BITWISE_FIELDS[table]):
        return []
    
    field_map = BITWISE_FIELDS[table][field]
    return [
        label for bit_value, label in field_map.items()
        if value & bit_value == bit_value
    ]