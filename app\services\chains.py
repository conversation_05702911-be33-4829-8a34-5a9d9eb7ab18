"""
ChatBI查询服务的LCEL链模块。

这个模块定义了用于查询处理流程的LangChain Expression Language (LCEL)链。
每个链都有特定的责任：
1. sql_gen_chain: 从自然语言问题生成精确高效的SQL查询
2. interpretation_chain: 根据用户的提问，立刻用自然流畅的语言告诉用户你将如何一步步为他分析数据
3. final_summary_chain: 根据查询结果生成最终的洞察报告

"""

import re
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnableLambda

from .llm_clients import sql_llm, interpretation_llm
from .prompts import SQL_GEN_SYSTEM, INTERPRETATION_SYSTEM, FINAL_SUMMARY_SYSTEM


def clean_sql(sql: str) -> str:
    """
    清洗SQL查询字符串，去除可能的Markdown代码块和额外的空格。

    Args:
        sql (str): 可能包含markdown格式的原始SQL字符串

    Returns:
        str: 清洗后可执行的SQL字符串
    """
    # 移除开头的```sql或```
    sql = re.sub(r"^```(?:sql)?\s*", "", sql, flags=re.IGNORECASE)
    # 移除结尾的```
    sql = re.sub(r"\s*```$", "", sql)
    # 清理行：去除空白字符并移除空行
    lines = [line.strip() for line in sql.splitlines()]
    return "\n".join([l for l in lines if l])


# --- 链1: SQL生成链 ---
# 目的：从自然语言问题生成精确高效的SQL查询
sql_gen_prompt = ChatPromptTemplate.from_messages([("system", SQL_GEN_SYSTEM)])
sql_gen_chain = (
    {"question": lambda x: x["question"]}
    | sql_gen_prompt
    | sql_llm
    | StrOutputParser()
    | RunnableLambda(clean_sql)
)


# --- 链2: 解读链 ---
# 目的：为用户提供关于分析计划的即时反馈
interpretation_prompt = ChatPromptTemplate.from_messages([("system", INTERPRETATION_SYSTEM)])
interpretation_chain = (
    {"question": lambda x: x["question"]}
    | interpretation_prompt
    | interpretation_llm
    | StrOutputParser()
)


# --- 链3: 最终总结链 ---
# 目的：基于实际查询结果生成有洞察力的总结
final_summary_prompt = ChatPromptTemplate.from_messages([("system", FINAL_SUMMARY_SYSTEM)])
final_summary_chain = (
      final_summary_prompt
    | interpretation_llm  # 为保持一致性使用解读LLM
    | StrOutputParser()
)
