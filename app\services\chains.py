"""
ChatBI查询服务的LCEL链模块。

这个模块定义了用于查询处理流程的LangChain Expression Language (LCEL)链。
每个链都有特定的责任：
1. sql_gen_chain: 从自然语言问题生成精确高效的SQL查询，支持多表关联和位运算字段
2. interpretation_chain: 根据用户的提问，立刻用自然流畅的语言告诉用户你将如何一步步为他分析数据
3. final_summary_chain: 根据查询结果生成最终的洞察报告

集成了schema_manager的表关系和位运算字段处理功能。
"""

import re
from typing import Dict, Any
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnableLambda

from .llm_clients import sql_llm, interpretation_llm
from .prompts import SQL_GEN_SYSTEM, INTERPRETATION_SYSTEM, FINAL_SUMMARY_SYSTEM
from .schema_manager import get_related_tables, decode_bitwise_field, BITWISE_FIELDS


def clean_sql(sql: str) -> str:
    """
    清洗SQL查询字符串，去除可能的Markdown代码块和额外的空格。

    Args:
        sql (str): 可能包含markdown格式的原始SQL字符串

    Returns:
        str: 清洗后可执行的SQL字符串
    """
    # 移除开头的```sql或```
    sql = re.sub(r"^```(?:sql)?\s*", "", sql, flags=re.IGNORECASE)
    # 移除结尾的```
    sql = re.sub(r"\s*```$", "", sql)
    # 清理行：去除空白字符并移除空行
    lines = [line.strip() for line in sql.splitlines()]
    return "\n".join([l for l in lines if l])


def validate_sql_syntax(sql: str) -> Dict[str, Any]:
    """
    验证SQL语法的基本正确性，特别是位运算字段的使用。

    Args:
        sql (str): 待验证的SQL字符串

    Returns:
        Dict[str, Any]: 包含验证结果和建议的字典
    """
    validation_result = {
        "is_valid": True,
        "warnings": [],
        "suggestions": []
    }

    # 检查位运算字段是否正确使用
    bitwise_pattern = r'\b(officer|title)\s*=\s*\d+'
    if re.search(bitwise_pattern, sql, re.IGNORECASE):
        validation_result["warnings"].append(
            "检测到位运算字段使用了等号(=)操作符，建议使用位运算语法 (field & value) = value"
        )
        validation_result["suggestions"].append(
            "将 'officer = 4' 改为 '(officer & 4) = 4'"
        )

    # 检查是否包含必要的表名
    if "erp_expert" not in sql.lower():
        validation_result["warnings"].append("查询中未包含主表 erp_expert")

    return validation_result


def enhance_sql_with_context(sql: str, question: str) -> str:
    """
    基于问题上下文增强SQL查询，添加必要的优化。

    Args:
        sql (str): 原始SQL查询
        question (str): 用户问题

    Returns:
        str: 增强后的SQL查询
    """
    # 如果是统计查询且没有LIMIT，添加合理的LIMIT
    if any(keyword in question.lower() for keyword in ['统计', '数量', '多少', '排名', '前']):
        if 'limit' not in sql.lower() and 'count(' not in sql.lower():
            # 对于非聚合查询，添加LIMIT以提高性能
            if not any(func in sql.lower() for func in ['count(', 'sum(', 'avg(', 'max(', 'min(']):
                sql = sql.rstrip(';') + ' LIMIT 100;'

    return sql


# --- 链1: 增强的SQL生成链 ---
# 目的：从自然语言问题生成精确高效的SQL查询，支持多表关联和位运算字段
def create_enhanced_sql_chain():
    """创建增强的SQL生成链，集成schema_manager功能"""

    def process_sql_generation(inputs: Dict[str, Any]) -> str:
        """处理SQL生成的完整流程"""
        question = inputs["question"]

        # 1. 生成原始SQL
        prompt = ChatPromptTemplate.from_messages([("system", SQL_GEN_SYSTEM)])
        raw_sql = (prompt | sql_llm | StrOutputParser()).invoke({"question": question})

        # 2. 清洗SQL
        cleaned_sql = clean_sql(raw_sql)

        # 3. 验证SQL语法
        validation = validate_sql_syntax(cleaned_sql)
        if validation["warnings"]:
            print(f"SQL验证警告: {validation['warnings']}")

        # 4. 基于上下文增强SQL
        enhanced_sql = enhance_sql_with_context(cleaned_sql, question)

        return enhanced_sql

    return RunnableLambda(process_sql_generation)

# 创建增强的SQL生成链实例
sql_gen_chain = create_enhanced_sql_chain()


# --- 链2: 解读链 ---
# 目的：为用户提供关于分析计划的即时反馈
interpretation_prompt = ChatPromptTemplate.from_messages([("system", INTERPRETATION_SYSTEM)])
interpretation_chain = (
    {"question": lambda x: x["question"]}
    | interpretation_prompt
    | interpretation_llm
    | StrOutputParser()
)


# --- 链3: 最终总结链 ---
# 目的：基于实际查询结果生成有洞察力的总结
final_summary_prompt = ChatPromptTemplate.from_messages([("system", FINAL_SUMMARY_SYSTEM)])
final_summary_chain = (
      final_summary_prompt
    | interpretation_llm  # 为保持一致性使用解读LLM
    | StrOutputParser()
)
