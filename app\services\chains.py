"""
LCEL chains for the ChatBI query service.

这个模块定义了用于查询处理流程的LangChain Expression Language (LCEL)链。
每个链都有特定的责任：
1. sql_gen_chain: 从自然语言问题生成精确高效的SQL查询
2. interpretation_chain: 根据用户的提问，立刻用自然流畅的语言告诉用户你将如何一步步为他分析数据
3. final_summary_chain: 根据查询结果生成最终的洞察报告

"""

import re
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnableLambda

from .llm_clients import sql_llm, interpretation_llm
from .prompts import SQL_GEN_SYSTEM, INTERPRETATION_SYSTEM, FINAL_SUMMARY_SYSTEM


def clean_sql(sql: str) -> str:
    """
    清洗SQL查询字符串，去除可能的Markdown代码块和额外的空格。

    Args:
        sql (str): Raw SQL string that may contain markdown formatting
        
    Returns:
        str: Cleaned SQL string ready for execution
    """
    # Remove ```sql or ``` at the beginning
    sql = re.sub(r"^```(?:sql)?\s*", "", sql, flags=re.IGNORECASE)
    # Remove ``` at the end
    sql = re.sub(r"\s*```$", "", sql)
    # Clean up lines: strip whitespace and remove empty lines
    lines = [line.strip() for line in sql.splitlines()]
    return "\n".join([l for l in lines if l])


# --- Chain 1: SQL Generation Chain ---
# Purpose: Generate precise and efficient SQL queries from natural language questions
sql_gen_prompt = ChatPromptTemplate.from_messages([("system", SQL_GEN_SYSTEM)])
sql_gen_chain = (
    {"question": lambda x: x["question"]}
    | sql_gen_prompt
    | sql_llm
    | StrOutputParser()
    | RunnableLambda(clean_sql)
)


# --- Chain 2: Interpretation Chain ---
# Purpose: Provide immediate feedback to users about the analysis plan
interpretation_prompt = ChatPromptTemplate.from_messages([("system", INTERPRETATION_SYSTEM)])
interpretation_chain = (
    {"question": lambda x: x["question"]}
    | interpretation_prompt
    | interpretation_llm
    | StrOutputParser()
)


# --- Chain 3: Final Summary Chain ---
# Purpose: Generate insightful summaries based on actual query results
final_summary_prompt = ChatPromptTemplate.from_messages([("system", FINAL_SUMMARY_SYSTEM)])
final_summary_chain = (
      final_summary_prompt
    | interpretation_llm  # Using interpretation LLM for consistency
    | StrOutputParser()
)
