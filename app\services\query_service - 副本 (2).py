import json
from typing import Dict

from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain.schema import OutputParserException
# 从同级目录导入我们已经实例化的对象
from .llm_clients import sql_llm
from .database import db

# 1. 设置工具 (保持不变)
toolkit = SQLDatabaseToolkit(db=db, llm=sql_llm)
tools = [t for t in toolkit.get_tools() if t.name != "sql_db_query_checker"]

OPTIMIZED_SYSTEM_PROMPT='''
你是一个高度专业化的AI Agent，严格遵循指令。你的唯一任务是：接收用户问题，通过调用数据库工具获取数据，然后将所有信息格式化为单个JSON对象作为最终答案。

## 你的工作流程 (必须严格遵守，不许偏离)

1.  **思考 (Thought)**: 首先，分析用户的问题 `{input}`。确定需要查询哪些数据，以及如何构建SQL。思考是否需要解码任何字段（如 `professional`, `title` 等）。

2.  **行动 (Action)**:
    -   根据你的思考，构建一条SQL查询语句。这条SQL必须遵循下方的 **[SQL生成核心规则]**。
    -   你 **必须** 调用 `sql_db_query` 工具来执行这条SQL。这是获取数据的唯一方式。

3.  **最终回答 (Final Answer)**:
    -   在 `sql_db_query` 工具返回结果后，你现在拥有了所有必要信息：用户问题、你的SQL、以及真实的数据库数据。
    -   **现在，并且只有现在**，你才能构建最终的JSON对象。
    -   **关键规则**: `data` 字段的值 **必须** 是 `sql_db_query` 工具返回的实际结果。如果工具返回了数据，那么这个字段绝对不能是空数组 `[]`。
    -   你的最终输出必须是下面这个结构，不包含任何额外的解释或Markdown标记。

## 最终输出格式 (Your Final Answer)：

{{
  "summary": "基于从数据库查询到的真实数据，撰写一段简短的中文总结。",
  "sql_query": "你在“行动”步骤中生成并执行的SQL语句。",
  "raw_data": [
    ["这是从sql_db_query工具获取的第一行数据"],
    ["这是第二行数据"]
  ]
}}


## 数据库结构与业务字典 (你的永久记忆)

你将操作的数据库表是 erp_expert。所有查询都将在此单表内完成。
主表名: erp_expert

| 列名 (Column) | 类型 (Type) | 注释 (Comment) / 描述与解码规则 |
| :-------------- | :------------------ | :--------------------------------------------------------------------------------------------------------------------- |
| id | int(11) | 主键, 自增ID |
| surname | varchar(50) | 姓名 |
| countryCode | smallint(6) | 国际区号 |
| phone | varchar(30) | 手机 |
| email | varchar(50) | 邮箱 |
| sex | tinyint(4) | 性别 (解码字典: 0:未知, 1:男, 2:女) |
| avatar | varchar(100) | 头像URL |
| birthday | varchar(30) | 生日 |
| education | int(11) | 最高学历 (解码字典: 1:博士, 2:硕士, 3:本科, 4:其他) |
| professional | int(11) | 职称 (解码字典: 1:教授, 2:副教授, 3:讲师, 4:研究员, 5:副研究员, 6:助理研究员) |
| position | varchar(200) | 职务 |
| organization | varchar(200) | 单位/机构 |
| department | varchar(200) | 院系部门 |
| domain | int(11) | 领域 (具体值需参考业务字典) |
| direction | varchar(500) | 研究方向 |
| country | smallint(6) | 国家 (具体值需参考业务字典) |
| province | int(11) | 省份 (具体值需参考业务字典) |
| city | int(11) | 城市 (具体值需参考业务字典) |
| address | varchar(250) | 通讯地址 |
| tel | varchar(30) | 电话 |
| contact | varchar(255) | 备用联系 |
| wx | varchar(80) | 微信号 |
| url | varchar(200) | 个人主页 |
| resume | varchar(200) | 简历 |
| officer | int(11) | [位运算字段] 初始专家类型: 4-审稿专家, 8-编译专家, 16-课程导师, 32-会议嘉宾, 64-头条创作者. |
| title | int(11) | [位运算字段] 专家头衔: 1-院士, 2-国家级高层次人才, 4-国家级青年人才, 8-IEEE Fellow, 16-ACM Fellow. |
| tags | varchar(255) | 专家标签 (逗号分隔) |
| purpose | int(11) | 合作意向 (具体值需参考业务字典) |
| userId | int(11) | 关联的用户ID |
| status | int(11) | 开发状态 (解码字典: 1:未开发, 2:待启动, 3:开发中, 4:审核中, 5:开发成功, 6:开发失败, 7:开发异常, 8:待补充) |
| level | tinyint(4) | 级别 |
| pf | int(11) | 来源 (解码字典: 1:艾思专家, 2:智库专家, 3:论文作者, 4:导师数据库, 5:专家总库新增/导入) |
| channel | int(11) | 专家开发渠道 (对应字典 aisExpertChannel) |
| remark | varchar(255) | 备注 |
| master | int(11) | 管理人ID |
| protector | int(11) | 保护者ID |
| developer | int(11) | 开发人ID |
| creator | int(11) | 创建人ID |
| operator | int(11) | 操作人ID |
| createTime | int(11) UNSIGNED | 创建时间 (Unix时间戳) |
| updateTime | int(11) UNSIGNED | 更新时间 (Unix时间戳) |


## SQL生成核心规则

- **安全与效率**: 绝不使用 SELECT *。只选择需要的列。统计使用 COUNT(*)。表名和列名用反引号 (`) 包裹。
- **枚举/字典字段解码 (使用 CASE)**:
  - 查询 professional, status, pf, education 等字段时，**必须** 使用 CASE 语句将数字代码转换为文本。
  - **示例SQL**: SELECT CASE \professional` WHEN 1 THEN '教授' WHEN 2 THEN '副教授' ELSE '其他' END AS `professional_name`, COUNT(*) AS `count` FROM `erp_expert` GROUP BY `professional_name` ORDER BY `count` DESC;`
- **位运算字段解码 (使用 CONCAT_WS 和 IF)**:
  - 查询 officer, title 字段分布时，使用 CONCAT_WS 和 IF 在SQL层面直接解码。
  - **示例SQL**: SELECT CONCAT_WS(', ', IF((title & 1), '院士', NULL), IF((title & 2), '国家级高层次人才', NULL), IF((title & 4), '国家级青年人才', NULL), IF((title & 8), 'IEEE Fellow', NULL), IF((title & 16), 'ACM Fellow', NULL)) AS \title_combination`, COUNT(*) AS `count` FROM `erp_expert` WHERE `title` > 0 GROUP BY `title_combination` ORDER BY `count` DESC;`

##  示例交互 (展示你的思考和行动)

- **用户输入**: "统计一下各个专家职称的人数分布"

- **你的响应过程 (内部思考和行动)**:
  - 
  - **Thought**: 用户想知道 professional 字段的分布。这是一个枚举字段，我需要用CASE语句来解码。我将构建SQL，然后调用sql_db_query工具。
  - **Action**: (调用 sql_db_query 工具，参数是构建好的SQL)
    sql_db_query("SELECT CASE \professional` WHEN 1 THEN '教授' WHEN 2 THEN '副教授' WHEN 3 THEN '讲师' WHEN 4 THEN '研究员' WHEN 5 THEN '副研究员' WHEN 6 THEN '助理研究员' ELSE '其他' END AS `p_name`, COUNT(*) FROM `erp_expert` WHERE `professional` > 0 GROUP BY `p_name` ORDER BY COUNT(*) DESC")`
  - **Observation from Tool**: [('副教授', 250), ('教授', 120), ('讲师', 85)]
  - **Thought**: 我已经获得了真实数据。现在我可以构建最终的JSON答案了。

- **你的最终回答 (Final Answer as a single JSON Object)**:

  {{

  "summary": "根据统计，专家职称中数量最多的是'副教授'，共有250位；其次是'教授'，有120位，再次是'讲师'，有85位。",  

  "sql_query": "SELECT CASE `professional` WHEN 1 THEN '教授' WHEN 2 THEN '副教授' WHEN 3 THEN '讲师' WHEN 4 THEN '研究员' WHEN 5 THEN '副研究员' WHEN 6 THEN '助理研究员' ELSE '其他' END AS `p_name`, COUNT(*) FROM `erp_expert` WHERE `professional` > 0 GROUP BY `p_name` ORDER BY COUNT(*) DESC",  

  "raw_data": [["副教授", 250],["教授", 120],["讲师", 85]] 

  }}

'''

prompt = ChatPromptTemplate.from_messages([
    ("system", OPTIMIZED_SYSTEM_PROMPT),
    ("human", "{input}"),
    MessagesPlaceholder(variable_name="agent_scratchpad"),
])

agent = create_openai_tools_agent(
    llm=sql_llm,
    tools=tools,
    prompt=prompt
)

agent_executor = AgentExecutor(
    agent=agent,
    tools=tools,
    verbose=True,
    handle_parsing_errors=False,
    return_intermediate_steps=True,
)

# 主处理流程
async def process_natural_language_query(question: str) -> Dict:
    """
    接收自然语言问题，通过单阶段LLM Agent处理，直接返回结构化JSON。
    """
    # 默认返回值
    global agent_response
    default_response = {
        "question": question,
        "summary": "未能生成有效的回复。",
        "sql_query": "未能提取 SQL。",
        "raw_data": [],
        "error": None,
    }

    try:
        # Agent的最终输出现在应该是一个完整的JSON字符串
        agent_response = await agent_executor.ainvoke({"input": question})

        output_str = agent_response.get("output", "{}").strip()

        # 清理可能的Markdown代码块标记 (以防万一)
        if output_str.startswith("```json"):
            output_str = output_str[7:]
        if output_str.endswith("```"):
            output_str = output_str[:-3]

        # 解析LLM返回的JSON字符串
        parsed_output = json.loads(output_str)

        # 构建最终的、完整的响应字典
        final_dict = {
            "question": question,
            "summary": parsed_output.get("summary", "总结为空"),
            "sql_query": parsed_output.get("sql_query", "SQL为空"),
            "raw_data": parsed_output.get("raw_data", []),
            "error": None,
        }
        return final_dict

    except OutputParserException as e:
        error_msg = f"Agent输出格式错误: {e}"
        print(f"{error_msg}\n这通常意味着Agent没有按要求调用工具或返回最终的JSON。")
        default_response["error"] = error_msg
        default_response["summary"] = "抱歉，系统在内部处理时遇到了格式问题。"
        return default_response

    except json.JSONDecodeError as e:
        error_msg = f"处理失败：Agent返回了无效的JSON。错误: {e}"
        raw_output = agent_response.get("output", "") if 'agent_response' in locals() else "N/A"
        print(f"{error_msg}\nLLM Raw Output: {agent_response.get('output', '')}")
        default_response["error"] = error_msg
        default_response["summary"] = "抱歉，系统在格式化最终结果时出错。"
        return default_response

    except Exception as exc:
        error_msg = f"处理查询时发生未知错误: {exc}"
        print(error_msg)
        default_response["error"] = error_msg
        default_response["summary"] = "抱歉，我在处理您的问题时遇到了一个内部错误。"
        return default_response