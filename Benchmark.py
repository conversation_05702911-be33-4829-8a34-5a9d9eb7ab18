import asyncio
import time
import os
import tiktoken
import pandas as pd
from openai import AsyncOpenAI
from dotenv import load_dotenv
from typing import List, Dict, Any

# --- 配置区 ---

# 加载.env文件中的环境变量
load_dotenv()

# 从环境变量初始化OpenAI客户端
# 脚本会自动读取OPENAI_API_KEY和OPENAI_BASE_URL（如果设置了）
client = AsyncOpenAI()

# 要测试的模型列表
MODELS_TO_TEST = [
    "deepseek-v3-0324"
]

# 测试用的Prompt列表
PROMPTS = [
    "你好，请做个自我介绍。",
    "写一个关于一只勇敢的小猫去外太空探险的短故事，大约200字。",
    "用Python写一个快速排序算法的实现。",
    "解释一下什么是Transformer模型，以及它在自然语言处理中的作用。",
    "给我五个关于未来城市交通的创新想法，并简要说明每个想法的优缺点。"
]

# --- 核心代码 ---

# 初始化tiktoken编码器
try:
    encoding = tiktoken.get_encoding("cl100k_base")
except Exception:
    encoding = tiktoken.encoding_for_model("gpt-4")


def count_tokens(text: str) -> int:
    """使用tiktoken计算字符串的token数量"""
    if not text:
        return 0
    return len(encoding.encode(text))


async def run_single_test(model: str, prompt: str) -> Dict[str, Any]:
    """对单个模型和Prompt运行一次完整的API测试"""

    # 初始化指标
    ttft = -1.0
    total_latency = -1.0
    output_tokens = 0
    full_response_content = ""
    error = None

    start_time = time.perf_counter()
    first_token_time = -1.0

    try:
        # 使用流式API调用
        stream = await client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            stream=True,
            temperature=0.7,  # 保持一定的随机性
            max_tokens=1024,
        )

        async for chunk in stream:
            # 检查是否有内容返回
            content = chunk.choices[0].delta.content
            if content:
                if first_token_time < 0:
                    first_token_time = time.perf_counter()
                    ttft = first_token_time - start_time

                full_response_content += content

    except Exception as e:
        error = f"API错误: {type(e).__name__}"

    end_time = time.perf_counter()
    total_latency = end_time - start_time

    # --- 后处理和计算 ---
    if not error:
        output_tokens = count_tokens(full_response_content)

    # 计算TPS (每秒token数)
    tps = 0.0
    if output_tokens > 1 and ttft > 0 and total_latency > ttft:
        generation_time = total_latency - ttft
        tps = (output_tokens - 1) / generation_time if generation_time > 0 else float('inf')

    # 计算输入token数
    prompt_tokens = count_tokens(prompt)

    return {
        "Model": model,
        "Prompt Snippet": f"'{prompt[:30]}...'",
        "Prompt Tokens": prompt_tokens,
        "Output Tokens": output_tokens,
        "TTFT (s)": f"{ttft:.3f}" if ttft > 0 else "N/A",
        "Total Latency (s)": f"{total_latency:.3f}",
        "Tokens/Sec (TPS)": f"{tps:.2f}" if tps > 0 else "N/A",
        "Error": error
    }


async def main():
    """主函数，运行所有测试并打印结果"""
    print("开始LLM API基准测试...")
    results = []

    tasks = [run_single_test(model, prompt) for model in MODELS_TO_TEST for prompt in PROMPTS]

    total_tasks = len(tasks)
    print(f"总共要运行的测试数: {total_tasks}")

    for i, future in enumerate(asyncio.as_completed(tasks)):
        result = await future
        results.append(result)
        print(f"完成测试 {i + 1}/{total_tasks}: 模型={result['Model']}, 提示={result['Prompt Snippet']}")

    # --- 整理并打印结果 ---
    if not results:
        print("没有结果可显示。")
        return

    df = pd.DataFrame(results)

    print("\n--- 基准测试结果 ---")
    print(df.to_string())

    # 计算并打印每个模型的平均值
    print("\n--- 每个模型的平均值 ---")

    # 转换数值列，忽略错误
    numeric_cols = ['TTFT (s)', 'Total Latency (s)', 'Tokens/Sec (TPS)']
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # 使用 groupby 计算每个模型的平均性能
    avg_results = df.groupby('Model')[numeric_cols].mean().reset_index()
    avg_results = avg_results.round(3)  # 保留3位小数

    print(avg_results.to_string(index=False))


if __name__ == "__main__":
    # 在Windows上，可能需要设置不同的事件循环策略
    # if os.name == 'nt':
    #     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())