#!/usr/bin/env python3
"""
ChatBI优化功能测试脚本

测试schema_manager集成后的各项功能：
1. 位运算字段处理
2. 表关联查询
3. SQL生成和验证
4. 结果处理和解码
"""

import sys
import os
import asyncio
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.schema_manager import (
    get_related_tables, 
    decode_bitwise_field, 
    get_join_condition,
    BITWISE_FIELDS,
    TABLE_RELATIONSHIPS
)
from app.services.chains import validate_sql_syntax, enhance_sql_with_context
from app.services.query_service import process_query_results


def test_schema_manager():
    """测试schema_manager的基本功能"""
    print("=== 测试 Schema Manager ===")
    
    # 测试获取关联表
    print("\n1. 测试获取关联表:")
    related_tables = get_related_tables("erp_expert")
    print(f"erp_expert的关联表数量: {len(related_tables)}")
    for table_info in related_tables[:3]:  # 只显示前3个
        print(f"  - {table_info['table']} ({table_info['type']})")
    
    # 测试JOIN条件获取
    print("\n2. 测试JOIN条件:")
    field, foreign_key = get_join_condition("erp_expert", "erp_expert_info")
    print(f"erp_expert -> erp_expert_info: {field} -> {foreign_key}")
    
    # 测试位运算字段解码
    print("\n3. 测试位运算字段解码:")
    # 测试officer字段 (4=审稿专家, 8=编译专家)
    officer_value = 12  # 4 + 8 = 审稿专家 + 编译专家
    decoded_officer = decode_bitwise_field("erp_expert", "officer", officer_value)
    print(f"officer值 {officer_value} 解码为: {decoded_officer}")
    
    # 测试title字段 (1=院士, 8=IEEE Fellow)
    title_value = 9  # 1 + 8 = 院士 + IEEE Fellow
    decoded_title = decode_bitwise_field("erp_expert", "title", title_value)
    print(f"title值 {title_value} 解码为: {decoded_title}")


def test_sql_validation():
    """测试SQL验证功能"""
    print("\n=== 测试 SQL 验证 ===")
    
    # 测试错误的位运算语法
    print("\n1. 测试错误的位运算语法:")
    bad_sql = "SELECT * FROM erp_expert WHERE officer = 4"
    validation = validate_sql_syntax(bad_sql)
    print(f"SQL: {bad_sql}")
    print(f"验证结果: {validation}")
    
    # 测试正确的位运算语法
    print("\n2. 测试正确的位运算语法:")
    good_sql = "SELECT * FROM erp_expert WHERE (officer & 4) = 4"
    validation = validate_sql_syntax(good_sql)
    print(f"SQL: {good_sql}")
    print(f"验证结果: {validation}")


def test_sql_enhancement():
    """测试SQL增强功能"""
    print("\n=== 测试 SQL 增强 ===")
    
    # 测试添加LIMIT
    print("\n1. 测试自动添加LIMIT:")
    original_sql = "SELECT * FROM erp_expert WHERE name LIKE '%张%'"
    question = "查找姓张的专家"
    enhanced_sql = enhance_sql_with_context(original_sql, question)
    print(f"原始SQL: {original_sql}")
    print(f"增强SQL: {enhanced_sql}")
    
    # 测试统计查询不添加LIMIT
    print("\n2. 测试统计查询:")
    count_sql = "SELECT COUNT(*) FROM erp_expert WHERE (title & 1) = 1"
    question = "统计院士数量"
    enhanced_sql = enhance_sql_with_context(count_sql, question)
    print(f"原始SQL: {count_sql}")
    print(f"增强SQL: {enhanced_sql}")


def test_result_processing():
    """测试结果处理功能"""
    print("\n=== 测试结果处理 ===")
    
    # 模拟查询结果
    print("\n1. 测试位运算字段结果处理:")
    mock_data = [
        (1, "张三", 12, 9),  # officer=12(审稿+编译), title=9(院士+IEEE Fellow)
        (2, "李四", 4, 8),   # officer=4(审稿), title=8(IEEE Fellow)
    ]
    
    # 模拟包含位运算字段的SQL
    sql_with_bitwise = "SELECT id, name, officer, title FROM erp_expert WHERE (officer & 4) = 4"
    
    result_info = process_query_results(mock_data, sql_with_bitwise)
    print(f"原始数据: {mock_data}")
    print(f"处理结果: {result_info}")


def test_table_relationships():
    """测试表关系定义"""
    print("\n=== 测试表关系定义 ===")
    
    print(f"\n1. 总关系表数量: {len(TABLE_RELATIONSHIPS)}")
    
    print("\n2. erp_expert的关联关系:")
    for relation_name, relation_info in TABLE_RELATIONSHIPS["erp_expert"].items():
        print(f"  - {relation_name}: {relation_info['related_table']} ({relation_info['relation_type']})")
    
    print(f"\n3. 位运算字段定义:")
    for table, fields in BITWISE_FIELDS.items():
        print(f"  表 {table}:")
        for field, mappings in fields.items():
            print(f"    字段 {field}: {len(mappings)} 个值")


async def test_integration():
    """集成测试（需要数据库连接）"""
    print("\n=== 集成测试 ===")
    print("注意: 此测试需要数据库连接，如果没有配置数据库，将跳过")
    
    try:
        from app.services.query_service import get_data_payload
        
        # 测试简单查询
        print("\n1. 测试简单查询:")
        question = "有多少专家？"
        result = await get_data_payload(question)
        print(f"问题: {question}")
        print(f"生成的SQL: {result.get('sql_query', 'SQL生成失败')}")
        
        # 测试位运算查询
        print("\n2. 测试位运算查询:")
        question = "有多少审稿专家？"
        result = await get_data_payload(question)
        print(f"问题: {question}")
        print(f"生成的SQL: {result.get('sql_query', 'SQL生成失败')}")
        
    except Exception as e:
        print(f"集成测试跳过，原因: {e}")


def main():
    """主测试函数"""
    print("ChatBI优化功能测试开始...\n")
    
    # 基础功能测试
    test_schema_manager()
    test_sql_validation()
    test_sql_enhancement()
    test_result_processing()
    test_table_relationships()
    
    # 异步集成测试
    print("\n开始异步集成测试...")
    asyncio.run(test_integration())
    
    print("\n=== 测试完成 ===")
    print("所有基础功能测试已完成。如需完整测试，请确保:")
    print("1. 数据库连接配置正确")
    print("2. LLM API配置正确")
    print("3. 运行 python test_optimizations.py")


if __name__ == "__main__":
    main()
