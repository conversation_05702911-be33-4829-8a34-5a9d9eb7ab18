import re
import json
import pymysql
from typing import Any, Dict, List, Tuple

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
from langchain_core.runnables import (RunnablePassthrough, RunnableLambda, RunnableSequence, RunnableConfig)

from .llm_clients import sql_llm
from .database import db



def clean_sql(sql: str) -> str:
    """
    去掉可能的 Markdown 代码块标记和前后多余文本，
    只保留纯 SQL。
    """
    # 去掉 ```sql 或 ``` 开头
    sql = re.sub(r"^```(?:sql)?\s*", "", sql, flags=re.IGNORECASE)
    # 去掉结尾的 ```
    sql = re.sub(r"\s*```$", "", sql)
    # 多行清理：去掉每行前后的空白
    lines = [line.strip() for line in sql.splitlines()]
    # 丢弃空行，再拼回去
    return "\n".join([l for l in lines if l])

# ------------------------------------------------------------------------------
# 节点 1：SQL 生成 LLM
# ------------------------------------------------------------------------------
SQL_GEN_SYSTEM = """
你是一个专家级的MySQL数据分析AI助手，请你完成下面两个任务：
1.根据用户的提问和提供的数据库结构，生成一条精准、高效的SQL查询语句。
2.对数据的解读和特征描述，例如：“根据数据可知，共有12条数据。商品品类为1个，销售额总和为1.27万，销售额平均值为1,058。2024-04月的销售额最高为2,822，占比为22.23%，2024-03月的销售额最低为343，占比为2.70%。销售额随时间变化的趋势如图表所示，整体上呈波动上升趋势，其中2024-01月至2024-02月的销售额有所增长，2024-02月至2024-03月的销售额显著下降，2024-03月至2024-04月的销售额大幅上升，2024-04月至2024-05月的销售额有所下降，2024-05月至2024-12月的销售额逐渐上升。##### 数据总览
数据表包含2024年1月至12月乳制品销售额数据，共12条记录。全年销售额总和为1.27万元，月均销售额1,058元。其中4月销售额达2,822元为峰值（占比22.23%），3月销售额343元为谷值（占比2.7%）。”（中文输出）。

你将操作的数据库表是 erp_expert。所有查询都将在此单表内完成。
主表名: erp_expert，结构:

| 列名 (Column) | 类型 (Type) | 注释 (Comment) / 描述与解码规则 |
| :-------------- | :------------------ | :--------------------------------------------------------------------------------------------------------------------- |
| id | int(11) | 主键, 自增ID |
| surname | varchar(50) | 姓名 |
| countryCode | smallint(6) | 国际区号 |
| phone | varchar(30) | 手机 |
| email | varchar(50) | 邮箱 |
| sex | tinyint(4) | 性别 (解码字典: 0:未知, 1:男, 2:女) |
| avatar | varchar(100) | 头像URL |
| birthday | varchar(30) | 生日 |
| education | int(11) | 最高学历 (解码字典: 1:博士, 2:硕士, 3:本科, 4:其他) |
| professional | int(11) | 职称 (解码字典: 1:教授, 2:副教授, 3:讲师, 4:研究员, 5:副研究员, 6:助理研究员) |
| position | varchar(200) | 职务 |
| organization | varchar(200) | 单位/机构 |
| department | varchar(200) | 院系部门 |
| domain | int(11) | 领域 (具体值需参考业务字典) |
| direction | varchar(500) | 研究方向 |
| country | smallint(6) | 国家 (具体值需参考业务字典) |
| province | int(11) | 省份 (具体值需参考业务字典) |
| city | int(11) | 城市 (具体值需参考业务字典) |
| address | varchar(250) | 通讯地址 |
| tel | varchar(30) | 电话 |
| contact | varchar(255) | 备用联系 |
| wx | varchar(80) | 微信号 |
| url | varchar(200) | 个人主页 |
| resume | varchar(200) | 简历 |
| officer | int(11) | [位运算字段] 初始专家类型: 4-审稿专家, 8-编译专家, 16-课程导师, 32-会议嘉宾, 64-头条创作者. |
| title | int(11) | [位运算字段] 专家头衔: 1-院士, 2-国家级高层次人才, 4-国家级青年人才, 8-IEEE Fellow, 16-ACM Fellow. |
| tags | varchar(255) | 专家标签 (逗号分隔) |
| purpose | int(11) | 合作意向 (具体值需参考业务字典) |
| userId | int(11) | 关联的用户ID |
| status | int(11) | 开发状态 (解码字典: 1:未开发, 2:待启动, 3:开发中, 4:审核中, 5:开发成功, 6:开发失败, 7:开发异常, 8:待补充) |
| level | tinyint(4) | 级别 |
    | pf | int(11) | 来源 (解码字典: 1:艾思专家, 2:智库专家, 3:论文作者, 4:导师数据库, 5:专家总库新增/导入) |
| channel | int(11) | 专家开发渠道 (对应字典 aisExpertChannel) |
| remark | varchar(255) | 备注 |
| master | int(11) | 管理人ID |
| protector | int(11) | 保护者ID |
| developer | int(11) | 开发人ID |
| creator | int(11) | 创建人ID |
| operator | int(11) | 操作人ID |
| createTime | int(11) UNSIGNED | 创建时间 (Unix时间戳) |
| updateTime | int(11) UNSIGNED | 更新时间 (Unix时间戳) |


**SQL生成核心规则**:
- **安全与效率**: 绝不使用 `SELECT *`。只选择需要的列。统计使用 `COUNT(*)`。表名和列名用反引号 (`) 包裹。
- **枚举/字典字段解码 (使用 `CASE`)**: 查询 `professional`, `status`, `pf` 等字段时，必须使用 `CASE` 语句将数字代码转换为文本。
- **位运算字段解码 (使用 `CONCAT_WS` 和 `IF`)**: 查询 `officer`, `title` 字段分布时，使用 `CONCAT_WS` 和 `IF` 在SQL层面直接解码。

**用户提问**: {question}

返回格式：
---
中文解析
---
SQL
"""

sql_gen_prompt = ChatPromptTemplate.from_messages([
    ("system", SQL_GEN_SYSTEM),
    ("human", "{question}")
])

sql_gen_chain = (
    sql_gen_prompt
    | sql_llm
    | StrOutputParser()
    | RunnableLambda(lambda txt: {
          "analysis": txt.split('---')[0].strip(),
          "sql_query": clean_sql(txt.split('---')[-1])
      })
)


# ------------------------------------------------------------------------------
# 节点 2：本地 SQL 查询 Runnable
# ------------------------------------------------------------------------------
def run_sql(sql: str) -> List[Tuple[Any, ...]]:
    """
    使用 pymysql 执行 SQL，返回 list[tuple] 形式的查询结果。
    """
    conn = pymysql.connect(
        host=db.host,
        user=db.user,
        password=db.password,
        database=db.database,
        charset="utf8mb4",
        cursorclass=pymysql.cursors.Cursor
    )
    try:
        with conn.cursor() as cur:
            cur.execute(sql)
            return cur.fetchall()
    finally:
        conn.close()

sql_query_runnable = RunnableLambda(run_sql)


# ------------------------------------------------------------------------------
# 节点 3：答案生成 LLM
# ------------------------------------------------------------------------------
ANSWER_SYSTEM = """
你是一个数据分析师和沟通专家。
你的任务是基于用户原始提问和数据库查询返回的原始数据，生成一个清晰、友好的总结，并将所有信息格式化为前端需要的JSON对象。

**用户原始提问**: {question}

**执行的SQL查询**:
```sql
{sql_query}
数据库返回的原始数据 (Python元组列表):
{raw_data}

你的任务:
1.分析数据: 理解原始数据的含义。如果数据是[('SQL_EXECUTION_ERROR', ...)]，说明SQL执行失败，你的总结需要友好地告知用户此问题。

2.生成总结 (summary): 对数据的解读和特征描述，例如：“根据数据可知，共有12条数据。商品品类为1个，销售额总和为1.27万，销售额平均值为1,058。2024-04月的销售额最高为2,822，占比为22.23%，2024-03月的销售额最低为343，占比为2.70%。销售额随时间变化的趋势如图表所示，整体上呈波动上升趋势，其中2024-01月至2024-02月的销售额有所增长，2024-02月至2024-03月的销售额显著下降，2024-03月至2024-04月的销售额大幅上升，2024-04月至2024-05月的销售额有所下降，2024-05月至2024-12月的销售额逐渐上升。##### 数据总览
数据表包含2024年1月至12月乳制品销售额数据，共12条记录。全年销售额总和为1.27万元，月均销售额1,058元。其中4月销售额达2,822元为峰值（占比22.23%），3月销售额343元为谷值（占比2.7%）。”（中文输出）。

3.格式化输出: 将所有信息（总结、SQL、原始数据）组装成一个JSON对象。
请严格按照以下JSON格式输出，不要包含任何额外的解释或Markdown标记:
{{
  "summary": "这里是你对数据的详细解读和特征描述。",
  "sql_query": "这里是执行过的SQL查询语句。",
  "raw_data": [
    ["row1_col1", row1_col2],
    ["row2_col1", row2_col2]
  ]
}}

"""

answer_prompt = ChatPromptTemplate.from_messages([
    ("system", ANSWER_SYSTEM),
    ("human", "原始问题: {question}\nSQL: {sql_query}\n数据:\n{raw_data}")
])

answer_chain = (
    answer_prompt
    | sql_llm               # 可复用同一个 llm，也可替换为别的模型
    | JsonOutputParser()    # 直接输出 Python dict
)


# ------------------------------------------------------------------------------
# LCEL 工作流拼接
# ------------------------------------------------------------------------------
workflow: RunnableSequence = (
    # ① 构建一个字典，保留 question 原文 & 生成 sql_query
    {
        "question": RunnablePassthrough(),
        "sql_query": sql_gen_chain,
    }
    # ② 执行 SQL 并把结果注入字典
    | RunnableLambda(lambda d: {
        "question": d["question"],
        "sql_query": d["sql_query"],
        "raw_data": run_sql(d["sql_query"]),
    })
    # ③ 调用答案生成 LLM，输出最终 JSON
    | answer_chain
)


# ------------------------------------------------------------------------------
# 对外调用接口
# ------------------------------------------------------------------------------

def process_natural_language_query(
    question: str,
    config: RunnableConfig = None
) -> Dict[str, Any]:
    """
    同步版本：接收自然语言问题，返回结构化的 JSON dict。
    """
    result = workflow.invoke(question, config=config)
    # 如果 answer_chain 返回的是 dict，直接返回；否则尝试 json.loads
    if isinstance(result, dict):
        return result
    try:
        return json.loads(result)
    except Exception:
        raise ValueError(f"无法解析 LLM 输出为 JSON: {result!r}")


async def process_natural_language_query_async(
    question: str,
    config: RunnableConfig = None
) -> Dict[str, Any]:
    """
    异步版本：接收自然语言问题，返回结构化的 JSON dict。
    """
    result = await workflow.ainvoke(question, config=config)
    if isinstance(result, dict):
        return result
    try:
        return json.loads(result)
    except Exception:
        raise ValueError(f"无法解析 LLM 输出为 JSON: {result!r}")

