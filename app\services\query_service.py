"""
ChatBI应用的查询服务模块。

这个模块提供了 ChatBI 应用的主要查询处理服务。
它接收自然语言查询，将其转换为SQL查询，执行查询，并提供带数据分析见解的流式响应。
集成了schema_manager的表关系和位运算字段处理功能。

"""

import json
import pymysql
import asyncio
from typing import Any, Dict, List, Tuple, AsyncGenerator, Optional

from langchain_core.runnables import RunnableConfig

# 导入数据库连接
from .database import db
# 导入预配置的LCEL链
from .chains import sql_gen_chain, interpretation_chain, final_summary_chain
# 导入schema管理功能
from .schema_manager import decode_bitwise_field, get_related_tables, BITWISE_FIELDS


def run_sql(sql: str) -> List[Tuple[Any, ...]]:
    """
    执行SQL查询并返回结果。

    Args:
        sql (str): 要执行的SQL查询语句

    Returns:
        List[Tuple[Any, ...]]: 查询结果列表
    """
    conn = pymysql.connect(
        host=db.host,
        user=db.user,
        password=db.password,
        database=db.database,
        charset="utf8mb4",
        cursorclass=pymysql.cursors.Cursor
    )
    try:
        with conn.cursor() as cur:
            cur.execute(sql)
            result = cur.fetchall()
            return list(result)  # Convert tuple to list to match return type annotation
    except Exception as e:
        print(f"SQL执行错误: {e}")
        # 返回一个特定格式的错误信息，方便前端处理
        return [("SQL_EXECUTION_ERROR", str(e))]
    finally:
        conn.close()


def process_query_results(raw_data: List[Tuple[Any, ...]], sql: str) -> Dict[str, Any]:
    """
    处理查询结果，解码位运算字段和增强数据展示。

    Args:
        raw_data: 原始查询结果
        sql: 执行的SQL语句

    Returns:
        Dict[str, Any]: 处理后的结果数据
    """
    if not raw_data or raw_data[0][0] in ["SQL_EXECUTION_ERROR", "API_ERROR"]:
        return {"processed_data": raw_data, "metadata": {"has_bitwise_fields": False}}

    processed_data = []
    has_bitwise_fields = False

    # 检查SQL中是否包含位运算字段
    sql_lower = sql.lower()
    bitwise_fields_in_query = []

    for table, fields in BITWISE_FIELDS.items():
        for field in fields.keys():
            if field in sql_lower:
                bitwise_fields_in_query.append((table, field))
                has_bitwise_fields = True

    # 处理每行数据
    for row in raw_data:
        processed_row = list(row)

        # 如果查询包含位运算字段，尝试解码
        if has_bitwise_fields and len(processed_row) > 0:
            # 这里可以根据具体的查询结构来解码位运算字段
            # 由于不知道具体的列顺序，我们提供一个通用的处理方式
            for i, value in enumerate(processed_row):
                if isinstance(value, int) and value > 0:
                    # 尝试解码可能的位运算字段
                    for table, field in bitwise_fields_in_query:
                        decoded_values = decode_bitwise_field(table, field, value)
                        if decoded_values:
                            # 添加解码信息到行数据中（可选）
                            processed_row.append(f"解码_{field}: {', '.join(decoded_values)}")
                            break

        processed_data.append(tuple(processed_row))

    return {
        "processed_data": processed_data,
        "metadata": {
            "has_bitwise_fields": has_bitwise_fields,
            "bitwise_fields": bitwise_fields_in_query,
            "row_count": len(processed_data)
        }
    }


async def get_data_payload(question: str, config: Optional[RunnableConfig] = None) -> Dict[str, Any]:
    """
    异步获取数据载荷的辅助函数，集成了结果处理功能。

    Args:
        question: 用户问题
        config: 运行配置

    Returns:
        Dict[str, Any]: 包含SQL查询、原始数据和处理后数据的字典
    """
    try:
        # 生成SQL查询
        sql_query = await sql_gen_chain.ainvoke({"question": question}, config)
        print(f"生成的SQL查询: {sql_query}")

        # 执行SQL查询
        loop = asyncio.get_running_loop()
        raw_data = await loop.run_in_executor(None, run_sql, sql_query)

        # 处理查询结果
        result_info = process_query_results(raw_data, sql_query)

        return {
            "sql_query": sql_query,
            "raw_data": raw_data,
            "processed_data": result_info["processed_data"],
            "metadata": result_info["metadata"]
        }
    except Exception as e:
        print(f"get_data_payload中的错误: {e}")
        # 返回错误信息，避免异步任务异常
        return {
            "sql_query": "-- SQL生成失败，请检查API配置",
            "raw_data": [("API_ERROR", f"LLM API调用失败: {str(e)}")],
            "processed_data": [("API_ERROR", f"LLM API调用失败: {str(e)}")],
            "metadata": {"has_bitwise_fields": False, "error": True}
        }

async def query_streaming_service(
    question: str,
    config: Optional[RunnableConfig] = None
) -> AsyncGenerator[str, None]:
    """
    处理自然语言查询的异步生成器服务。
    该函数通过并行和串行调度，实现了三阶段流水线。
    """
    #
    # --- 阶段一: 并行启动"计划"文本流和"后台数据获取"任务 ---
    #

    # 创建后台数据获取任务，它会立即开始执行
    data_task = asyncio.create_task(get_data_payload(question, config))

    # 同时，立刻开始流式输出"计划"文本，为用户提供即时反馈
    try:
        plan_stream = interpretation_chain.astream({"question": question}, config)
        async for chunk in plan_stream:
            payload = json.dumps({"chunk": chunk})
            yield f"event: summary_chunk\ndata: {payload}\n\n"
    except Exception as e:
        # 如果此阶段失败，取消后台任务以节省资源
        data_task.cancel()
        
        # 发送统一格式的错误事件
        error_info = {"source": "interpretation", "message": f"抱歉，AI服务暂时不可用: {str(e)}"}
        error_payload = json.dumps(error_info)
        yield f"event: error\ndata: {error_payload}\n\n"
        # 终止生成器
        return

    #
    # --- 阶段二: 等待数据任务完成，并发送中间数据 ---
    #

    # 等待后台数据任务完成
    data_payload = await data_task
    sql_query = data_payload["sql_query"]
    raw_data = data_payload["raw_data"]
    processed_data = data_payload["processed_data"]
    metadata = data_payload["metadata"]

    # 发送SQL语句
    sql_payload = json.dumps({"sql_query": sql_query})
    yield f"event: sql_generated\ndata: {sql_payload}\n\n"

    # 发送处理好的原始数据，用于前端渲染图表
    data_list = [list(row) for row in raw_data]
    data_final_payload = json.dumps({
        "raw_data": data_list,
        "metadata": metadata
    })
    yield f"event: data_final\ndata: {data_final_payload}\n\n"

    # 如果有位运算字段，发送额外的解码信息
    if metadata.get("has_bitwise_fields", False):
        bitwise_info = {
            "bitwise_fields": metadata["bitwise_fields"],
            "decoded_data": [list(row) for row in processed_data]
        }
        bitwise_payload = json.dumps(bitwise_info)
        yield f"event: bitwise_decoded\ndata: {bitwise_payload}\n\n"

    #
    # --- 阶段三: 基于真实数据，生成并流式输出最终的"数据总结" ---
    #

    # 发送一个特殊事件，告知前端"最终总结"即将开始
    # 前端可以利用这个事件来添加一个分割线或提示
    yield f"event: final_summary_start\ndata: {{}}\n\n"

    # 启动最终总结链
    final_stream = final_summary_chain.astream({
        "question": question,
        "sql_query": sql_query,
        "raw_data": str(raw_data) # 将原始数据转为字符串传入提示模板
    }, config)

    async for chunk in final_stream:
        payload = json.dumps({"chunk": chunk})
        yield f"event: summary_chunk\ndata: {payload}\n\n"

    #
    # 所有流程结束，发送终止信号
    #
    yield "event: end_stream\ndata: done\n\n"