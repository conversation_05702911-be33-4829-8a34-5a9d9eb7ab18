"""
ChatBI应用的查询服务模块。

这个模块提供了 ChatBI 应用的主要查询处理服务。
它接收自然语言查询，将其转换为SQL查询，执行查询，并提供带数据分析见解的流式响应。

"""

import json
import pymysql
import asyncio
from typing import Any, Dict, List, Tuple, AsyncGenerator, Optional

from langchain_core.runnables import RunnableConfig

# 导入数据库连接
from .database import db
# 导入预配置的LCEL链
from .chains import sql_gen_chain, interpretation_chain, final_summary_chain


def run_sql(sql: str) -> List[Tuple[Any, ...]]:
    # 使用 pymysql 执行 SQL，返回 list[tuple] 形式的查询结果
    conn = pymysql.connect(
        host=db.host,
        user=db.user,
        password=db.password,
        database=db.database,
        charset="utf8mb4",
        cursorclass=pymysql.cursors.Cursor
    )
    try:
        with conn.cursor() as cur:
            cur.execute(sql)
            result = cur.fetchall()
            return list(result)  # Convert tuple to list to match return type annotation
    except Exception as e:
        print(f"SQL执行错误: {e}")
        # 返回一个特定格式的错误信息，方便前端处理
        return [("SQL_EXECUTION_ERROR", str(e))]
    finally:
        conn.close()


async def get_data_payload(question: str, config: Optional[RunnableConfig] = None) -> Dict[str, Any]:
    # 这是一个辅助的异步函数，用于封装后台的数据获取任务
    try:
        sql_query = await sql_gen_chain.ainvoke({"question": question}, config)
        loop = asyncio.get_running_loop()
        # run_sql 是同步IO操作，使用 run_in_executor 避免阻塞事件循环
        raw_data = await loop.run_in_executor(None, run_sql, sql_query)
        return {"sql_query": sql_query, "raw_data": raw_data}
    except Exception as e:
        print(f"get_data_payload中的错误: {e}")
        # 返回错误信息，避免异步任务异常
        return {
            "sql_query": "-- SQL生成失败，请检查API配置",
            "raw_data": [("API_ERROR", f"LLM API调用失败: {str(e)}")]
        }

async def query_streaming_service(
    question: str,
    config: Optional[RunnableConfig] = None
) -> AsyncGenerator[str, None]:
    """
    处理自然语言查询的异步生成器服务。
    该函数通过并行和串行调度，实现了三阶段流水线。
    """
    #
    # --- 阶段一: 并行启动"计划"文本流和"后台数据获取"任务 ---
    #

    # 创建后台数据获取任务，它会立即开始执行
    data_task = asyncio.create_task(get_data_payload(question, config))

    # 同时，立刻开始流式输出"计划"文本，为用户提供即时反馈
    try:
        plan_stream = interpretation_chain.astream({"question": question}, config)
        async for chunk in plan_stream:
            payload = json.dumps({"chunk": chunk})
            yield f"event: summary_chunk\ndata: {payload}\n\n"
    except Exception as e:
        # 如果此阶段失败，取消后台任务以节省资源
        data_task.cancel()
        
        # 发送统一格式的错误事件
        error_info = {"source": "interpretation", "message": f"抱歉，AI服务暂时不可用: {str(e)}"}
        error_payload = json.dumps(error_info)
        yield f"event: error\ndata: {error_payload}\n\n"
        # 终止生成器
        return

    #
    # --- 阶段二: 等待数据任务完成，并发送中间数据 ---
    #

    # 等待后台数据任务完成
    data_payload = await data_task
    sql_query = data_payload["sql_query"]
    raw_data = data_payload["raw_data"]

    # 发送SQL语句
    sql_payload = json.dumps({"sql_query": sql_query})
    yield f"event: sql_generated\ndata: {sql_payload}\n\n"

    # 发送处理好的原始数据，用于前端渲染图表
    data_list = [list(row) for row in raw_data]
    data_final_payload = json.dumps({"raw_data": data_list})
    yield f"event: data_final\ndata: {data_final_payload}\n\n"

    #
    # --- 阶段三: 基于真实数据，生成并流式输出最终的"数据总结" ---
    #

    # 发送一个特殊事件，告知前端"最终总结"即将开始
    # 前端可以利用这个事件来添加一个分割线或提示
    yield f"event: final_summary_start\ndata: {{}}\n\n"

    # 启动最终总结链
    final_stream = final_summary_chain.astream({
        "question": question,
        "sql_query": sql_query,
        "raw_data": str(raw_data) # 将原始数据转为字符串传入提示模板
    }, config)

    async for chunk in final_stream:
        payload = json.dumps({"chunk": chunk})
        yield f"event: summary_chunk\ndata: {payload}\n\n"

    #
    # 所有流程结束，发送终止信号
    #
    yield "event: end_stream\ndata: done\n\n"